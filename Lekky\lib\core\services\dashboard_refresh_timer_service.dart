import 'dart:async';
import 'package:flutter/widgets.dart';
import '../utils/logger.dart';
import '../utils/event_bus.dart';

/// Service for managing Dashboard periodic refresh timer
class DashboardRefreshTimerService with WidgetsBindingObserver {
  static final DashboardRefreshTimerService _instance =
      DashboardRefreshTimerService._internal();

  factory DashboardRefreshTimerService() => _instance;
  DashboardRefreshTimerService._internal();

  Timer? _refreshTimer;
  bool _isInitialized = false;
  bool _isAppInForeground = true;
  DateTime? _lastRefreshTime;

  /// 30-minute refresh interval
  static const Duration _refreshInterval = Duration(minutes: 30);

  /// Initialize the service and start monitoring app lifecycle
  void initialize() {
    if (_isInitialized) return;

    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
    Logger.info('DashboardRefreshTimerService: Initialized');
  }

  /// Dispose the service and cleanup resources
  void dispose() {
    if (!_isInitialized) return;

    WidgetsBinding.instance.removeObserver(this);
    _stopTimer();
    _isInitialized = false;
    Logger.info('DashboardRefreshTimerService: Disposed');
  }

  /// Start the 30-minute refresh timer
  void startTimer() {
    if (!_isAppInForeground) {
      Logger.info(
          'DashboardRefreshTimerService: App in background, timer not started');
      return;
    }

    _stopTimer(); // Cancel any existing timer

    _refreshTimer = Timer.periodic(_refreshInterval, (timer) {
      if (_isAppInForeground) {
        Logger.info(
            'DashboardRefreshTimerService: 30-minute timer triggered, firing refresh event');
        _triggerRefresh();
      }
    });

    _lastRefreshTime = DateTime.now();
    Logger.info(
        'DashboardRefreshTimerService: 30-minute refresh timer started');
  }

  /// Stop the refresh timer
  void stopTimer() {
    _stopTimer();
    Logger.info('DashboardRefreshTimerService: Timer stopped');
  }

  /// Reset the timer (restart from current time)
  void resetTimer() {
    if (_isAppInForeground) {
      startTimer();
      Logger.info('DashboardRefreshTimerService: Timer reset');
    }
  }

  /// Trigger immediate refresh and reset timer
  void triggerImmediateRefresh() {
    _triggerRefresh();
    resetTimer();
    Logger.info(
        'DashboardRefreshTimerService: Immediate refresh triggered and timer reset');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _isAppInForeground = true;
        Logger.info(
            'DashboardRefreshTimerService: App resumed, checking for refresh');
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _isAppInForeground = false;
        _stopTimer();
        Logger.info(
            'DashboardRefreshTimerService: App backgrounded, timer stopped');
        break;
      case AppLifecycleState.hidden:
        _isAppInForeground = false;
        Logger.info('DashboardRefreshTimerService: App hidden');
        break;
    }
  }

  /// Handle app resumed state
  void _handleAppResumed() {
    // Check if we need to refresh based on time elapsed
    if (_lastRefreshTime != null) {
      final timeSinceLastRefresh = DateTime.now().difference(_lastRefreshTime!);
      if (timeSinceLastRefresh >= _refreshInterval) {
        Logger.info(
            'DashboardRefreshTimerService: App resumed after ${timeSinceLastRefresh.inMinutes} minutes, triggering refresh');
        triggerImmediateRefresh();
        return;
      }
    }

    // Start timer for future refreshes
    startTimer();
  }

  /// Internal method to stop timer
  void _stopTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Trigger refresh by firing event
  void _triggerRefresh() {
    _lastRefreshTime = DateTime.now();
    EventBus().fire(EventType.dashboardPeriodicRefresh);
  }

  /// Get time until next refresh (for debugging/monitoring)
  Duration? getTimeUntilNextRefresh() {
    if (_refreshTimer == null || _lastRefreshTime == null) return null;

    final nextRefreshTime = _lastRefreshTime!.add(_refreshInterval);
    final now = DateTime.now();
    
    if (nextRefreshTime.isAfter(now)) {
      return nextRefreshTime.difference(now);
    }
    
    return Duration.zero;
  }

  /// Check if timer is currently active
  bool get isTimerActive => _refreshTimer != null && _refreshTimer!.isActive;

  /// Check if app is in foreground
  bool get isAppInForeground => _isAppInForeground;
}
